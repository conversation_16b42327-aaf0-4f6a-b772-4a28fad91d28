I'm a software developer, quantitative trader and entrepreneur。 Teaching machine learning, trading and software development. Author of 'Best Practices for Python'. 

我是一名软件工程师、量化交易人和创业者。《Python高效编程最佳实践指南》的作者。我也是一系列开源软件的开发者或者维护者。
>[!tip]
>我们教授《匡醍.量化24课》、《匡醍.因子分析与机器学习策略》和《匡醍.量化人的Numpy和Pandas》等系列课程，帮助你从入门到精通，完全掌握量化交易。课程都配有视频、在线运行的Notebook、习题和答疑。请前往公众号 Quantide 咨询

## 最新文章

<div class="as-grid m-t-md">
<div class="card-columns">
    
<div>
<h3>Z变换改造均线，一个12年前的策略为何仍能跑赢大盘？</h3>
<img src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/neom-tSwRu3Jh0EM-unsplash.jpg" style="height: 200px" align="right"/>
<p><span>内容摘要:<br></span>传统移动平均线（MA）是技术分析中常用的趋势跟踪指标，通过对股票价格或指数在一定天数内的平均值进行计算，以刻画其变动方向。MA 的计算天数越多，其平滑性越好，但随之而来的时滞（延迟）影响也越严重。这意味着 MA 指标在跟踪趋势时容易出现“跟不紧”甚至“跟不上”的情况，平滑性...</p>

<p><span style="margin-right:20px">发表于 2025-07-16 人气 934 </span><span><a href="https://www.jieyu.ai/blog/posts/factor-strategy/低延迟趋势线与交易择时/">点击阅读</a></span></p>

</div><!--end-article-->
<br/>
<br/>


<div>
<h3>π-thon以及他的朋友们</h3>
<img src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/hot/meme/π-thon.png" style="height: 200px" align="right"/>
<p><span>内容摘要:<br></span>最近的Python社区热闹异常。在6月中旬，Python发布了Python 3.14 beta3。它可不是一个普通的预发布版本 -- 它是第一个正式支持期待已久的自由线程或『无 GIL』的版本。而有没有GIL，绝对是Python发展史上的有一个分水岭。这个版本将在今年的程序...</p>

<p><span style="margin-right:20px">发表于 2025-07-15 人气 292 </span><span><a href="https://www.jieyu.ai/blog/posts/python/it-is-π-thon/">点击阅读</a></span></p>

</div><!--end-article-->
<br/>
<br/>


<div>
<h3>『匡醍译研报 02』 驯龙高手，从股谚到量化因子的工程化落地</h3>
<img src="https://images.jieyu.ai/images/hot/mybook/book-with-course.png" style="height: 200px" align="right"/>
<p><span>内容摘要:<br></span>上一期文章中，我们复现了研报的因子构建部分，分别是影线因子、威廉影线因子以及由此组合而来的 UBL 因子。这一期我们将对这些因子进行检验。<br><br>因子检验固然是因子挖掘中必不可少的一环，但它应该是一个 routine 的工作 -- 我们不应该每次都重新发明轮子。然而...</p>

<p><span style="margin-right:20px">发表于 2025-07-04 人气 407 </span><span><a href="https://www.jieyu.ai/blog/posts/papers/ubl-2/">点击阅读</a></span></p>

</div><!--end-article-->
<br/>
<br/>

</div>
</div>

更多精彩好文，请访问[匡醍量化](https://www.jieyu.ai)

