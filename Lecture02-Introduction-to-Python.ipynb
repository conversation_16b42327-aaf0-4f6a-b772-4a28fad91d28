{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://www.quantrocket.com\"><img alt=\"QuantRocket logo\" src=\"https://www.quantrocket.com/assets/img/notebook-header-logo.png\"></a>\n", "\n", "© Copyright Quantopian Inc.<br>\n", "© Modifications Copyright QuantRocket LLC<br>\n", "Licensed under the [Creative Commons Attribution 4.0](https://creativecommons.org/licenses/by/4.0/legalcode).<br>\n", "<a href=\"https://www.quantrocket.com/disclaimer/\">Disclaimer</a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["***\n", "[Quant Finance Lectures (adapted Quantopian Lectures)](Introduction.ipynb) › Lecture 2 - Introduction to Python\n", "***"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to Python\n", "\n", "by <PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://youtu.be/bQUWLkKzpxE?t=37\" target=\"_blank\">Quantopian video for this lecture ↗</a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Python is a good, jack-of-all-trades language to know. Here we will provide you with the basics so that you can feel confident going through our other lectures and understanding what is happening."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code Comments\n", "\n", "A comment is a note made by a programmer in the source code of a program. Its purpose is to clarify the source code and make it easier for people to follow along with what is happening. Anything in a comment is generally ignored when the code is actually run, making comments useful for including explanations and reasoning as well as removing specific lines of code that you may be unsure about. Comments in Python are created by using the pound symbol (`# Insert Text Here`). Including a `#` in a line of code will comment out anything that follows it."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# This is a comment\n", "# These lines of code will not change any values\n", "# Anything following the first # is not run as code"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You may hear text enclosed in triple quotes (`\"\"\" Insert Text Here \"\"\"`) referred to as multi-line comments, but this is not entirely accurate. This is a special type of `string` (a data type we will cover), called a `docstring`, used to explain the purpose of a function."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["' This is a special string '"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\" This is a special string \"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Make sure you read the comments within each code cell (if they are there). They will provide more real-time explanations of what is going on as you look at each line of code."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Variables\n", "\n", "Variables provide names for values in programming. If you want to save a value for later or repeated use, you give the value a name, storing the contents in a variable. Variables in programming work in a fundamentally similar way to variables in algebra, but in Python they can take on various different data types.\n", "\n", "The basic variable types that we will cover in this section are `integers`, `floating point numbers`, `booleans`, and `strings`. \n", "\n", "An `integer` in programming is the same as in mathematics, a round number with no values after the decimal point. We use the built-in `print` function here to display the values of our variables as well as their types!"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["50 <class 'int'>\n"]}], "source": ["my_integer = 50\n", "print(my_integer, type(my_integer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Variables, regardless of type, are assigned by using a single equals sign (`=`). Variables are case-sensitive so any changes in variation in the capitals of a variable name will reference a different variable entirely."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'One' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-4-6b74af782f90>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mone\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mOne\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'One' is not defined"]}], "source": ["one = 1\n", "print(One)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A `floating point` number, or a `float` is a fancy name for a real number (again as in mathematics). To define a `float`, we need to either include a decimal point or specify that the value is a float."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0 <class 'float'>\n", "1.0 <class 'float'>\n"]}], "source": ["my_float = 1.0\n", "print(my_float, type(my_float))\n", "my_float = float(1)\n", "print(my_float, type(my_float))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A variable of type `float` will not round the number that you store in it, while a variable of type `integer` will. This makes `floats` more suitable for mathematical calculations where you want more than just integers.\n", "\n", "Note that as we used the `float()` function to force an number to be considered a `float`, we can use the `int()` function to force a number to be considered an `int`."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3 <class 'int'>\n"]}], "source": ["my_int = int(3.14159)\n", "print(my_int, type(my_int))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `int()` function will also truncate any digits that a number may have after the decimal point!\n", "\n", "Strings allow you to include text as a variable to operate on. They are defined using either single quotes ('') or double quotes (\"\")."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This is a string with single quotes\n", "This is a string with double quotes\n"]}], "source": ["my_string = 'This is a string with single quotes'\n", "print(my_string)\n", "my_string = \"This is a string with double quotes\"\n", "print(my_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Both are allowed so that we can include apostrophes or quotation marks in a string if we so choose."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Jabberwocky\", by <PERSON>\n", "'Twas brillig, and the slithy toves / Did gyre and gimble in the wabe;\n"]}], "source": ["my_string = '\"Jabberwocky\", by <PERSON>'\n", "print(my_string)\n", "my_string = \"'Twas brillig, and the slithy toves / Did gyre and gimble in the wabe;\"\n", "print(my_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Booleans, or `bools` are binary variable types. A `bool` can only take on one of two values, these being `True` or `False`. There is much more to this idea of truth values when it comes to programming, which we cover later in the [Logical Operators](#Logical-Operators) of this notebook."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True <class 'bool'>\n"]}], "source": ["my_bool = True\n", "print(my_bool, type(my_bool))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are many more data types that you can assign as variables in Python, but these are the basic ones! We will cover a few more later as we move through this tutorial."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Math\n", "\n", "Python has a number of built-in math functions. These can be extended even further by importing the **math** package or by including any number of other calculation-based packages.\n", "\n", "All of the basic arithmetic operations are supported: `+`, `-`, `/`, and `*`. You can create exponents by using `**` and modular arithmetic is introduced with the mod operator, `%`."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Addition: 4\n", "Subtraction: 3\n", "Multiplication: 10\n", "Division: 5.0\n", "Exponentiation: 9\n"]}], "source": ["print('Addition:', 2 + 2)\n", "print('Subtraction:', 7 - 4)\n", "print('Multiplication:', 2 * 5)\n", "print('Division:', 10 / 2)\n", "print('Exponentiation:', 3**2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you are not familiar with the the mod operator, it operates like a remainder function. If we type $15 \\ \\% \\ 4$, it will return the remainder after dividing $15$ by $4$."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modulo: 3\n"]}], "source": ["print('Mo<PERSON>lo:', 15 % 4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Mathematical functions also work on variables!"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n"]}], "source": ["first_integer = 4\n", "second_integer = 5\n", "print(first_integer * second_integer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Python has a few built-in math functions. The most notable of these are:\n", "\n", "* `abs()`\n", "* `round()`\n", "* `max()`\n", "* `min()`\n", "* `sum()`\n", "\n", "These functions all act as you would expect, given their names. Calling `abs()` on a number will return its absolute value. The `round()` function will round a number to a specified number of decimal points (the default is $0$). Calling `max()` or `min()` on a collection of numbers will return, respectively, the maximum or minimum value in the collection. Calling `sum()` on a collection of numbers will add them all up. If you're not familiar with how collections of values in Python work, don't worry! We will cover collections in-depth in the next section. \n", "\n", "Additional math functionality can be added in with the `math` package."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import math"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The math library adds a long list of new mathematical functions to Python. Feel free to check out the [documentation](https://docs.python.org/3/library/math.html) for the full list and details. It concludes some mathematical constants"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pi: 3.141592653589793\n", "<PERSON><PERSON><PERSON>'s Constant: 2.718281828459045\n"]}], "source": ["print('Pi:', math.pi)\n", "print(\"<PERSON><PERSON><PERSON>'s Constant:\", math.e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As well as some commonly used math functions"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cosine of pi: -1.0\n"]}], "source": ["print('Cosine of pi:', math.cos(math.pi))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Collections\n", "### Lists\n", "\n", "A `list` in Python is an ordered collection of objects that can contain any data type. We define a `list` using brackets (`[]`)."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3]\n"]}], "source": ["my_list = [1, 2, 3]\n", "print(my_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can access and index the list by using brackets as well. In order to select an individual element, simply type the list name followed by the index of the item you are looking for in braces."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "3\n"]}], "source": ["print(my_list[0])\n", "print(my_list[2])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Indexing in Python starts from $0$. If you have a list of length $n$, the first element of the list is at index $0$, the second element is at index $1$, and so on and so forth. The final element of the list will be at index $n-1$. Be careful! Trying to access a non-existent index will cause an error."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The first, second, and third list elements: 1 2 3\n"]}, {"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-18-b7c80acbca97>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'The first, second, and third list elements:'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmy_list\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmy_list\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmy_list\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'Accessing outside the list bounds causes an error:'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmy_list\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m3\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mIndexError\u001b[0m: list index out of range"]}], "source": ["print('The first, second, and third list elements:', my_list[0], my_list[1], my_list[2])\n", "print('Accessing outside the list bounds causes an error:', my_list[3])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see the number of elements in a list by calling the `len()` function."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["print(len(my_list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can update and change a list by accessing an index and assigning new value."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3]\n", "[42, 2, 3]\n"]}], "source": ["print(my_list)\n", "my_list[0] = 42\n", "print(my_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is fundamentally different from how strings are handled. A `list` is mutable, meaning that you can change a `list`'s elements without changing the list itself. Some data types, like `strings`, are immutable, meaning you cannot change them at all. Once a `string` or other immutable data type has been created, it cannot be directly modified without creating an entirely new object."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'str' object does not support item assignment", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m<ipython-input-21-85065ecd61ab>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mmy_string\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"Strings never change\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mmy_string\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m'Z'\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mTypeError\u001b[0m: 'str' object does not support item assignment"]}], "source": ["my_string = \"Strings never change\"\n", "my_string[0] = 'Z'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we stated before, a list can contain any data type. Thus, lists can also contain strings."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['one', 'two', 'three']\n"]}], "source": ["my_list_2 = ['one', 'two', 'three']\n", "print(my_list_2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lists can also contain multiple different data types at once!"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["my_list_3 = [True, 'False', 42]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you want to put two lists together, they can be combined with a `+` symbol."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[42, 2, 3, 'one', 'two', 'three', True, 'False', 42]\n"]}], "source": ["my_list_4 = my_list + my_list_2 + my_list_3\n", "print(my_list_4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In addition to accessing individual elements of a list, we can access groups of elements through slicing."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["my_list = ['friends', 'romans', 'countrymen', 'lend', 'me', 'your', 'ears']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Slicing\n", "\n", "We use the colon (`:`) to slice lists. "]}, {"cell_type": "code", "execution_count": 26, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['countrymen', 'lend']\n"]}], "source": ["print(my_list[2:4])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Using `:` we can select a group of elements in the list starting from the first element indicated and going up to  (but not including) the last element indicated.\n", "\n", "We can also select everything after a certain point"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['romans', 'countrymen', 'lend', 'me', 'your', 'ears']\n"]}], "source": ["print(my_list[1:])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And everything before a certain point"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['friends', 'romans', 'countrymen', 'lend']\n"]}], "source": ["print(my_list[:4])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Using negative numbers will count from the end of the indices instead of from the beginning. For example, an index of `-1` indicates the last element of the list."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ears\n"]}], "source": ["print(my_list[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also add a third component to slicing. Instead of simply indicating the first and final parts of your slice, you can specify the step size that you want to take. So instead of taking every single element, you can take every other element."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['friends', 'countrymen', 'me', 'ears']\n"]}], "source": ["print(my_list[0:7:2])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we have selected the entire list (because `0:7` will yield elements `0` through `6`) and we have selected a step size of `2`. So this will spit out element `0` , element `2`, element `4`, and so on through the list element selected. We can skip indicating the beginning and end of our slice, only indicating the step, if we like."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['friends', 'countrymen', 'me', 'ears']\n"]}], "source": ["print(my_list[::2])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lists implictly select the beginning and end of the list when not otherwise specified."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['friends', 'romans', 'countrymen', 'lend', 'me', 'your', 'ears']\n"]}], "source": ["print(my_list[:])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With a negative step size we can even reverse the list!"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ears', 'your', 'me', 'lend', 'countrymen', 'romans', 'friends']\n"]}], "source": ["print(my_list[::-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Python does not have native matrices, but with lists we can produce a working fascimile. Other packages, such as `numpy`, add matrices as a separate data type, but in base Python the best way to create a matrix is to use a list of lists."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also use built-in functions to generate lists. In particular we will look at `range()` (because we will be using it later!). Range can take several different inputs and will return an iterator. We can see the items in the iterator by coercing it to a list."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n"]}], "source": ["b = 10\n", "my_list = range(b)\n", "print(list(my_list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Similar to our list-slicing methods from before, we can define both a start and an end for our range. This will return a list that includes the start and excludes the end, just like a slice."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n"]}], "source": ["a = 0\n", "b = 10\n", "my_list = range(a, b)\n", "print(list(my_list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also specify a step size. This again has the same behavior as a slice."]}, {"cell_type": "code", "execution_count": 36, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 2, 4, 6, 8]\n"]}], "source": ["a = 0\n", "b = 10\n", "step = 2\n", "my_list = range(a, b, step)\n", "print(list(my_list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON>\n", "\n", "A `tuple` is a data type similar to a list in that it can hold different kinds of data types. The key difference here is that a `tuple` is immutable. We define a `tuple` by separating the elements we want to include by commas. It is conventional to surround a `tuple` with parentheses."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('I', 'have', 30, 'cats')\n"]}], "source": ["my_tuple = 'I', 'have', 30, 'cats'\n", "print(my_tuple)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('I', 'have', 30, 'cats')\n"]}], "source": ["my_tuple = ('I', 'have', 30, 'cats')\n", "print(my_tuple)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As mentioned before, tuples are immutable. You can't change any part of them without defining a new tuple."]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'tuple' object does not support item assignment", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m<ipython-input-39-c821a6bdb1a6>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mmy_tuple\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m3\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m'dogs'\u001b[0m \u001b[0;31m# Attempts to change the 'cats' value stored in the the tuple to 'dogs'\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mTypeError\u001b[0m: 'tuple' object does not support item assignment"]}], "source": ["my_tuple[3] = 'dogs' # Attempts to change the 'cats' value stored in the the tuple to 'dogs'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can slice tuples the same way that you slice lists!"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('have', 30)\n"]}], "source": ["print(my_tuple[1:3])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And concatenate them the way that you would with strings!"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('I', 'have', 30, 'cats', 'make', 'that', 50)\n"]}], "source": ["my_other_tuple = ('make', 'that', 50)\n", "print(my_tuple + my_other_tuple)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can 'pack' values together, creating a tuple (as above), or we can 'unpack' values from a tuple, taking them out."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["make that 50\n"]}], "source": ["str_1, str_2, int_1 = my_other_tuple\n", "print(str_1, str_2, int_1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unpacking assigns each value of the tuple in order to each variable on the left hand side of the equals sign. Some functions, including user-defined functions, may return tuples, so we can use this to directly unpack them and access the values that we want."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sets\n", "\n", "A `set` is a collection of unordered, unique elements. It works almost exactly as you would expect a normal set of things in mathematics to work and is defined using braces (`{}`)."]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{4, 7, 'dogs', 'lizards', 42, 'man I just LOVE the number 4', 'the number 4'} <class 'set'>\n"]}], "source": ["things_i_like = {'dogs', 7, 'the number 4', 4, 4, 4, 42, 'lizards', 'man I just LOVE the number 4'}\n", "print(things_i_like, type(things_i_like))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note how any extra instances of the same item are removed in the final set. We can also create a `set` from a list, using the `set()` function."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'bats', 'dogs', 'lizards', 'sponges', 'cats', 'cows'}\n"]}], "source": ["animal_list = ['cats', 'dogs', 'dogs', 'dogs', 'lizards', 'sponges', 'cows', 'bats', 'sponges']\n", "animal_set = set(animal_list)\n", "print(animal_set) # Removes all extra instances from the list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Calling `len()` on a set will tell you how many elements are in it."]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n"]}], "source": ["print(len(animal_set))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Because a `set` is unordered, we can't access individual elements using an index. We can, however, easily check for membership (to see if something is contained in a set) and take the unions and intersections of sets by using the built-in set functions."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["'cats' in animal_set # Here we check for membership using the `in` keyword."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we checked to see whether the string 'cats' was contained within our `animal_set` and it returned `True`, telling us that it is indeed in our set.\n", "\n", "We can connect sets by using typical mathematical set operators, namely `|`, for union, and `&`, for intersection. Using `|` or `&` will return exactly what you would expect if you are familiar with sets in mathematics."]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'bats', 4, 7, 'dogs', 'lizards', 'sponges', 42, 'cats', 'cows', 'man I just LOVE the number 4', 'the number 4'}\n"]}], "source": ["print(animal_set | things_i_like) # You can also write things_i_like | animal_set with no difference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pairing two sets together with `|` combines the sets, removing any repetitions to make every set element unique."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'dogs', 'lizards'}\n"]}], "source": ["print(animal_set & things_i_like) # You can also write things_i_like & animal_set with no difference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pairing two sets together with `&` will calculate the intersection of both sets, returning a set that only contains what they have in common.\n", "\n", "If you are interested in learning more about the built-in functions for sets, feel free to check out the [documentation](https://docs.python.org/3/library/sets.html)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dictionaries\n", "\n", "Another essential data structure in Python is the dictionary. Dictionaries are defined with a combination of curly braces (`{}`) and colons (`:`). The braces define the beginning and end of a dictionary and the colons indicate key-value pairs. A dictionary is essentially a set of key-value pairs. The key of any entry must be an immutable data type. This makes both strings and tuples candidates. Keys can be both added and deleted.\n", "\n", "In the following example, we have a dictionary composed of key-value pairs where the key is a genre of fiction (`string`) and the value is a list of books (`list`) within that genre. Since a collection is still considered a single entity, we can use one to collect multiple variables or values into one key-value pair."]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["my_dict = {\"High Fantasy\": [\"Wheel of Time\", \"Lord of the Rings\"], \n", "           \"Sci-fi\": [\"Book of the New Sun\", \"Neuromancer\", \"Snow Crash\"],\n", "           \"Weird Fiction\": [\"At the Mountains of Madness\", \"The House on the Borderland\"]}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After defining a dictionary, we can access any individual value by indicating its key in brackets."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Book of the New Sun', 'Neuromancer', 'Snow Crash']\n"]}], "source": ["print(my_dict[\"Sci-fi\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also change the value associated with a given key"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I can't read\n"]}], "source": ["my_dict[\"Sci-fi\"] = \"I can't read\"\n", "print(my_dict[\"Sci-fi\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Adding a new key-value pair is as simple as defining it."]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Pillars of the Earth']\n"]}], "source": ["my_dict[\"Historical Fiction\"] = [\"Pillars of the Earth\"]\n", "print(my_dict[\"Historical Fiction\"])"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'High Fantasy': ['Wheel of Time', 'Lord of the Rings'], 'Sci-fi': \"I can't read\", 'Weird Fiction': ['At the Mountains of Madness', 'The House on the Borderland'], 'Historical Fiction': ['Pillars of the Earth']}\n"]}], "source": ["print(my_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## String Shenanigans\n", "\n", "We already know that strings are generally used for text. We can used built-in operations to combine, split, and format strings easily, depending on our needs.\n", "\n", "The `+` symbol indicates concatenation in string language. It will combine two strings into a longer string."]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Beware the Jabberwock, my son! /The jaws that bite, the claws that catch! /Beware the Jubjub bird, and shun /The frumious Bandersnatch!\"/\n"]}], "source": ["first_string = '\"Beware the Jabberwock, my son! /The jaws that bite, the claws that catch! /'\n", "second_string = 'Beware the Jubjub bird, and shun /The frumious Bandersnatch!\"/'\n", "third_string = first_string + second_string\n", "print(third_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Strings are also indexed much in the same way that lists are."]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The first letter is: S\n", "The last letter is: s\n", "The second to last letter is: u\n", "The first five characters are: <PERSON>\n", "Reverse it!: suoicodilaipxecitsiligarfilacrepuS\n"]}], "source": ["my_string = 'Supercalifragilisticexpialidocious'\n", "print('The first letter is:', my_string[0]) # Uppercase S\n", "print('The last letter is:', my_string[-1]) # lowercase s\n", "print('The second to last letter is:', my_string[-2]) # lowercase u\n", "print('The first five characters are:', my_string[0:5]) # Remember: slicing doesn't include the final element!\n", "print('Reverse it!:', my_string[::-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Built-in objects and classes often have special functions associated with them that are called methods. We access these methods by using a period ('.'). We will cover objects and their associated methods more in another lecture!\n", "\n", "Using string methods we can count instances of a character or group of characters."]}, {"cell_type": "code", "execution_count": 56, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Count of the letter i in Supercalifragilisticexpialidocious: 7\n", "Count of \"li\" in the same word: 3\n"]}], "source": ["print('Count of the letter i in Supercalifragilisticexpialidocious:', my_string.count('i'))\n", "print('Count of \"li\" in the same word:', my_string.count('li'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also find the first instance of a character or group of characters in a string."]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The first time i appears is at index: 8\n"]}], "source": ["print('The first time i appears is at index:', my_string.find('i'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As well as replace characters in a string."]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All i's are now a's: Supercalafragalastacexpaaladocaous\n"]}], "source": ["print(\"All i's are now a's:\", my_string.replace('i', 'a'))"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It's raining cats and more cats\n"]}], "source": ["print(\"It's raining cats and dogs\".replace('dogs', 'more cats'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are also some methods that are unique to strings. The function `upper()` will convert all characters in a string to uppercase, while `lower()` will convert all characters in a string to lowercase!"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I CAN'T HEAR YOU\n", "i said hello\n"]}], "source": ["my_string = \"I can't hear you\"\n", "print(my_string.upper())\n", "my_string = \"I said HELLO\"\n", "print(my_string.lower())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### String Formatting\n", "\n", "Using the `format()` method we can add in variable values and generally format our strings."]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>\n"]}], "source": ["my_string = \"{0} {1}\".format('<PERSON>', '<PERSON>')\n", "print(my_string)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>\n"]}], "source": ["my_string = \"{1} {0}\".format('<PERSON>', '<PERSON>')\n", "print(my_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We use braces (`{}`) to indicate parts of the string that will be filled in later and we use the arguments of the `format()` function to provide the values to substitute. The numbers within the braces indicate the index of the value in the `format()` arguments."]}, {"cell_type": "markdown", "metadata": {}, "source": ["See the `format()` [documentation](https://docs.python.org/3/library/string.html#format-examples) for additional examples."]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you need some quick and dirty formatting, you can instead use the `%` symbol, called the string formatting operator. "]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["insert value here\n"]}], "source": ["print('insert %s here' % 'value')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `%` symbol basically cues <PERSON> to create a placeholder. Whatever character follows the `%` (in the string) indicates what sort of type the value put into the placeholder will have. This character is called a *conversion type*. Once the string has been closed, we need another `%` that will be followed by the values to insert. In the case of one value, you can just put it there. If you are inserting more than one value, they must be enclosed in a tuple."]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 13 cats in my apartment\n"]}], "source": ["print('There are %s cats in my %s' % (13, 'apartment'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In these examples, the `%s` indicates that Python should convert the values into strings. There are multiple conversion types that you can use to get more specific with the the formatting. See the string formatting [documentation](https://docs.python.org/3/library/stdtypes.html#printf-style-string-formatting) for additional examples and more complete details on use."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logical Operators\n", "### Basic Logic\n", "\n", "Logical operators deal with `boolean` values, as we briefly covered before. If you recall, a `bool` takes on one of two values, `True` or `False` (or $1$ or $0$). The basic logical statements that we can make are defined using the built-in comparators. These are `==` (equal), `!=` (not equal), `<` (less than), `>` (greater than), `<=` (less than or equal to), and `>=` (greater than or equal to)."]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["print(5 == 5)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["print(5 > 5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These comparators also work in conjunction with variables."]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["m = 2\n", "n = 23\n", "print(m < n)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can string these comparators together to make more complex logical statements using the logical operators `or`, `and`, and `not`. "]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Statement 1 truth value: True\n", "Statement 2 truth value: True\n", "Statement 1 and Statement 2: True\n"]}], "source": ["statement_1 = 10 > 2\n", "statement_2 = 4 <= 6\n", "print(\"Statement 1 truth value: {0}\".format(statement_1))\n", "print(\"Statement 2 truth value: {0}\".format(statement_2))\n", "print(\"Statement 1 and Statement 2: {0}\".format(statement_1 and statement_2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `or` operator performs a logical `or` calculation. This is an inclusive `or`, so if either component paired together by `or` is `True`, the whole statement will be `True`. The `and` statement only outputs `True` if all components that are `and`ed together are True. Otherwise it will output `False`. The `not` statement simply inverts the truth value of whichever statement follows it. So a `True` statement will be evaluated as `False` when a `not` is placed in front of it. Similarly, a `False` statement will become `True` when a `not` is in front of it.\n", "\n", "Say that we have two logical statements, or assertions, $P$ and $Q$. The truth table for the basic logical operators is as follows:\n", "\n", "|  P  |  Q  | `not` P| P `and` Q | P `or` Q|\n", "|:-----:|:-----:|:---:|:---:|:---:|\n", "| `True` | `True` | `False` | `True` | `True` |\n", "| `False` | `True` | `True` | `False` | `True` |\n", "| `True` | `False` | `False` | `False` | `True` |\n", "| `False` | `False` | `True` | `False` | `False` |\n", "\n", "We can string multiple logical statements together using the logical operators."]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["print(((2 < 3) and (3 > 0)) or ((5 > 6) and not (4 < 2)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Logical statements can be as simple or complex as we like, depending on what we need to express. Evaluating the above logical statement step by step we see that we are evaluating (`True and True`) `or` (`False and not False`). This becomes `True or (False and True`), subsequently becoming `True or False`, ultimately being evaluated as `True`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Truthiness\n", "\n", "Data types in Python have a fun characteristic called truthiness. What this means is that most built-in types will evaluate as either `True` or `False` when a boolean value is needed (such as with an if-statement). As a general rule, containers like strings, tuples, dictionaries, lists, and sets, will return `True` if they contain anything at all and `False` if they contain nothing."]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["# Similar to how float() and int() work, bool() forces a value to be considered a boolean!\n", "print(bool(''))"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["print(bool('I have character!'))"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["print(bool([]))"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["print(bool([1, 2, 3]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And so on, for the other collections and containers. `None` also evaluates as `False`. The number `1` is equivalent to `True` and the number `0` is equivalent to `False` as well, in a boolean context."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## If-statements\n", "\n", "We can create segments of code that only execute if a set of conditions is met. We use if-statements in conjunction with logical statements in order to create branches in our code. \n", "\n", "An `if` block gets entered when the condition is considered to be `True`. If condition is evaluated as `False`, the `if` block will simply be skipped unless there is an `else` block to accompany it. Conditions are made using either logical operators or by using the truthiness of values in Python. An if-statement is defined with a colon and a block of indented text."]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# This is the basic format of an if statement. This is a vacuous example. \n", "# The string \"Condition\" will always evaluated as True because it is a\n", "# non-empty string. he purpose of this code is to show the formatting of\n", "# an if-statement.\n", "if \"Condition\": \n", "    # This block of code will execute because the string is non-empty\n", "    # Everything on these indented lines\n", "    print(True)\n", "else:\n", "    # So if the condition that we examined with if is in fact False\n", "    # This block of code will execute INSTEAD of the first block of code\n", "    # Everything on these indented lines\n", "    print(False)\n", "# The else block here will never execute because \"Condition\" is a non-empty string."]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["i = 4\n", "if i == 5:\n", "    print('The variable i has a value of 5')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Because in this example `i = 4` and the if-statement is only looking for whether `i` is equal to `5`, the print statement will never be executed. We can add in an `else` statement to create a contingency block of code in case the condition in the if-statement is not evaluated as `True`."]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All lines in this indented block are part of this block\n", "The variable i is not equal to 5\n"]}], "source": ["i = 4\n", "if i == 5:\n", "    print(\"All lines in this indented block are part of this block\")\n", "    print('The variable i has a value of 5')\n", "else:\n", "    print(\"All lines in this indented block are part of this block\")\n", "    print('The variable i is not equal to 5')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can implement other branches off of the same if-statement by using `elif`, an abbreviation of \"else if\". We can include as many `elifs` as we like until we have exhausted all the logical branches of a condition."]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The variable i has a value of 1\n"]}], "source": ["i = 1\n", "if i == 1:\n", "    print('The variable i has a value of 1')\n", "elif i == 2:\n", "    print('The variable i has a value of 2')\n", "elif i == 3:\n", "    print('The variable i has a value of 3')\n", "else:\n", "    print(\"I don't care what i is\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also nest if-statements within if-statements to check for further conditions."]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i is divisible by both 2 and 5! Wow!\n"]}], "source": ["i = 10\n", "if i % 2 == 0:\n", "    if i % 3 == 0:\n", "        print('i is divisible by both 2 and 3! Wow!')\n", "    elif i % 5 == 0:\n", "        print('i is divisible by both 2 and 5! Wow!')\n", "    else:\n", "        print('i is divisible by 2, but not 3 or 5. Meh.')\n", "else:\n", "    print('I guess that i is an odd number. Boring.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Remember that we can group multiple conditions together by using the logical operators!"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5 is less than 10 and 12 is greater than 11! How novel and interesting!\n"]}], "source": ["i = 5\n", "j = 12\n", "if i < 10 and j > 11:\n", "    print('{0} is less than 10 and {1} is greater than 11! How novel and interesting!'.format(i, j))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can use the logical comparators to compare strings!"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["And so it was! For the glory of Rome!\n"]}], "source": ["my_string = \"Carthago delenda est\"\n", "if my_string == \"Carthago delenda est\":\n", "    print('And so it was! For the glory of Rome!')\n", "else:\n", "    print('War elephants are TERRIFYING. I am staying home.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As with other data types, `==` will check for whether the two things on either side of it have the same value. In this case, we compare whether the value of the strings are the same. Using `>` or `<` or any of the other comparators is not quite so intuitive, however, so we will stay from using comparators with strings in this lecture. Comparators will examine the [lexicographical order](https://en.wikipedia.org/wiki/Lexicographical_order) of the strings, which might be a bit more in-depth than you might like."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Some built-in functions return a boolean value, so they can be used as conditions in an if-statement. User-defined functions can also be constructed so that they return a boolean value. This will be covered later with function definition!\n", "\n", "The `in` keyword is generally used to check membership of a value within another value. We can check memebership in the context of an if-statement and use it to output a truth value."]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Those are my favorite vowels!\n"]}], "source": ["if 'a' in my_string or 'e' in my_string:\n", "    print('Those are my favorite vowels!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we use `in` to check whether the variable `my_string` contains any particular letters. We will later use `in` to iterate through lists!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loop Structures\n", "\n", "Loop structures are one of the most important parts of programming. The `for` loop and the `while` loop provide a way to repeatedly run a block of code repeatedly. A `while` loop will iterate until a certain condition has been met. If at any point after an iteration that condition is no longer satisfied, the loop terminates. A `for` loop will iterate over a sequence of values and terminate when the sequence has ended. You can instead include conditions within the `for` loop to decide whether it should terminate early or you could simply let it run its course."]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am looping! 4 more to go!\n", "I am looping! 3 more to go!\n", "I am looping! 2 more to go!\n", "I am looping! 1 more to go!\n", "I am looping! 0 more to go!\n"]}], "source": ["i = 5\n", "while i > 0: # We can write this as 'while i:' because 0 is False!\n", "    i -= 1\n", "    print('I am looping! {0} more to go!'.format(i))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "With `while` loops we need to make sure that something actually changes from iteration to iteration so that that the loop actually terminates. In this case, we use the shorthand `i -= 1` (short for `i = i - 1`) so that the value of `i` gets smaller with each iteration. Eventually `i` will be reduced to `0`, rendering the condition `False` and exiting the loop."]}, {"cell_type": "markdown", "metadata": {}, "source": ["A `for` loop iterates a set number of times, determined when you state the entry into the loop. In this case we are iterating over the list returned from `range()`. The `for` loop selects a value from the list, in order, and temporarily assigns the value of `i` to it so that operations can be performed with the value."]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am looping! I have looped 1 times!\n", "I am looping! I have looped 2 times!\n", "I am looping! I have looped 3 times!\n", "I am looping! I have looped 4 times!\n", "I am looping! I have looped 5 times!\n"]}], "source": ["for i in range(5):\n", "    print('I am looping! I have looped {0} times!'.format(i + 1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that in this `for` loop we use the `in` keyword. Use of the `in` keyword is not limited to checking for membership as in the if-statement example. You can iterate over any collection with a `for` loop by using the `in` keyword.\n", "\n", "In this next example, we will iterate over a `set` because we want to check for containment and add to a new set."]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'bats', 'dogs', 'cats', 'cows', 'humans'}\n"]}], "source": ["my_list = {'cats', 'dogs', 'lizards', 'cows', 'bats', 'sponges', 'humans'} # Lists all the animals in the world\n", "mammal_list = {'cats', 'dogs', 'cows', 'bats', 'humans'} # Lists all the mammals in the world\n", "my_new_list = set()\n", "for animal in my_list:\n", "    if animal in mammal_list:\n", "        # This adds any animal that is both in my_list and mammal_list to my_new_list\n", "        my_new_list.add(animal)\n", "        \n", "print(my_new_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two statements that are very helpful in dealing with both `for` and `while` loops. These are `break` and `continue`. If `break` is encountered at any point while a loop is executing, the loop will immediately end."]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["11\n", "12\n", "13\n", "14\n"]}], "source": ["i = 10\n", "while True:\n", "    if i == 14:\n", "        break\n", "    i += 1 # This is shorthand for i = i + 1. It increments i with each iteration.\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n"]}], "source": ["for i in range(5):\n", "    if i == 2:\n", "        break\n", "    print(i)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `continue` statement will tell the loop to immediately end this iteration and continue onto the next iteration of the loop."]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "4\n", "5\n"]}], "source": ["i = 0\n", "while i < 5:\n", "    i += 1\n", "    if i == 3:\n", "        continue\n", "    print(i)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This loop skips printing the number $3$ because of the `continue` statement that executes when we enter the if-statement. The code never sees the command to print the number $3$ because it has already moved to the next iteration. The `break` and `continue` statements are further tools to help you control the flow of your loops and, as a result, your code."]}, {"cell_type": "markdown", "metadata": {}, "source": ["The variable that we use to iterate over a loop will retain its value when the loop exits. Similarly, any variables defined within the context of the loop will continue to exist outside of it."]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am eternal! I am 0 and I exist everywhere!\n", "I am eternal! I am 1 and I exist everywhere!\n", "I am eternal! I am 2 and I exist everywhere!\n", "I am eternal! I am 3 and I exist everywhere!\n", "I am eternal! I am 4 and I exist everywhere!\n", "I persist! My value is 4\n", "I transcend the loop!\n"]}], "source": ["for i in range(5):\n", "    loop_string = 'I transcend the loop!'\n", "    print('I am eternal! I am {0} and I exist everywhere!'.format(i))\n", "\n", "print('I persist! My value is {0}'.format(i))\n", "print(loop_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also iterate over a dictionary!"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["my_dict = {'firstname' : '<PERSON><PERSON>', 'lastname' : '<PERSON><PERSON>', 'nemesis' : '<PERSON><PERSON>'}"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["firstname\n", "lastname\n", "nemesis\n"]}], "source": ["for key in my_dict:\n", "    print(key)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we just iterate over a dictionary without doing anything else, we will only get the keys. We can either use the keys to get the values, like so:"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Inigo\n", "<PERSON><PERSON>\n", "<PERSON><PERSON>\n"]}], "source": ["for key in my_dict:\n", "    print(my_dict[key])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or we can use the `items()` function to get both key and value at the same time."]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["firstname : <PERSON><PERSON>\n", "lastname : <PERSON><PERSON>\n", "nemesis : <PERSON><PERSON>\n"]}], "source": ["for key, value in my_dict.items():\n", "    print(key, ':', value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `items()` function creates a tuple of each key-value pair and the for loop unpacks that tuple into `key, value` on each separate execution of the loop!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Functions\n", "\n", "A function is a reusable block of code that you can call repeatedly to make calculations, output data, or really do anything that you want. This is one of the key aspects of using a programming language. To add to the built-in functions in Python, you can define your own!"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, world!\n"]}], "source": ["def hello_world():\n", "    \"\"\" Prints Hello, world! \"\"\"\n", "    print('Hello, world!')\n", "\n", "hello_world()"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, world!\n", "Hello, world!\n", "Hello, world!\n", "Hello, world!\n", "Hello, world!\n"]}], "source": ["for i in range(5):\n", "    hello_world()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Functions are defined with `def`, a function name, a list of parameters, and a colon. Everything indented below the colon will be included in the definition of the function.\n", "\n", "We can have our functions do anything that you can do with a normal block of code. For example, our `hello_world()` function prints a string every time it is called. If we want to keep a value that a function calculates, we can define the function so that it will `return` the value we want. This is a very important feature of functions, as any variable defined purely within a function will not exist outside of it."]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'in_function_string' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-95-fb168f245f67>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0msee_the_scope\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0min_function_string\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'in_function_string' is not defined"]}], "source": ["def see_the_scope():\n", "    in_function_string = \"I'm stuck in here!\"\n", "\n", "see_the_scope()\n", "print(in_function_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": [" The **scope** of a variable is the part of a block of code where that variable is tied to a particular value. Functions in Python have an enclosed scope, making it so that variables defined within them can only be accessed directly within them. If we pass those values to a return statement we can get them out of the function. This makes it so that the function call returns values so that you can store them in variables that have a greater scope.\n", " \n", "In this case specifically, including a return statement allows us to keep the string value that we define in the function."]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Anything you can do I can do better!\n"]}], "source": ["def free_the_scope():\n", "    in_function_string = \"Anything you can do I can do better!\"\n", "    return in_function_string\n", "my_string = free_the_scope()\n", "print(my_string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Just as we can get values out of a function, we can also put values into a function. We do this by defining our function with parameters."]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n", "20\n"]}], "source": ["def multiply_by_five(x):\n", "    \"\"\" Multiplies an input number by 5 \"\"\"\n", "    return x * 5\n", "\n", "n = 4\n", "print(n)\n", "print(multiply_by_five(n))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example we only had one parameter for our function, `x`. We can easily add more parameters, separating everything with a comma."]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [], "source": ["def calculate_area(length, width):\n", "    \"\"\" Calculates the area of a rectangle \"\"\"\n", "    return length * width"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Area: 50\n", "Length: 5\n", "Width: 10\n"]}], "source": ["l = 5\n", "w = 10\n", "print('Area:', calculate_area(l, w))\n", "print('Length:', l)\n", "print('Width:', w)"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [], "source": ["def calculate_volume(length, width, depth):\n", "    \"\"\" Calculates the volume of a rectangular prism \"\"\"\n", "    return length * width * depth"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we want to, we can define a function so that it takes an arbitrary number of parameters. We tell Python that we want this by using an asterisk (`*`)."]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["def sum_values(*args):\n", "    sum_val = 0\n", "    for i in args:\n", "        sum_val += i\n", "    return sum_val"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n", "150\n", "343\n"]}], "source": ["print(sum_values(1, 2, 3))\n", "print(sum_values(10, 20, 30, 40, 50))\n", "print(sum_values(4, 2, 5, 1, 10, 249, 25, 24, 13, 6, 4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The time to use `*args` as a parameter for your function is when you do not know how many values may be passed to it, as in the case of our sum function. The asterisk in this case is the syntax that tells Python that you are going to pass an arbitrary number of parameters into your function. These parameters are stored in the form of a tuple."]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'tuple'>\n"]}], "source": ["def test_args(*args):\n", "    print(type(args))\n", "\n", "test_args(1, 2, 3, 4, 5, 6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can put as many elements into the `args` tuple as we want to when we call the function. However, because `args` is a tuple, we cannot modify it after it has been created.\n", "\n", "The `args` name of the variable is purely by convention. You could just as easily name your parameter `*vars` or `*things`. You can treat the `args` tuple like you would any other tuple, easily accessing `arg`'s values and iterating over it, as in the above `sum_values(*args)` function."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our functions can return any data type. This makes it easy for us to create functions that check for conditions that we might want to monitor.\n", "\n", "Here we define a function that returns a boolean value. We can easily use this in conjunction with if-statements and  other situations that require a boolean."]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [], "source": ["def has_a_vowel(word):\n", "    \"\"\" \n", "    Checks to see whether a word contains a vowel \n", "    If it doesn't contain a conventional vowel, it\n", "    will check for the presence of 'y' or 'w'. Does\n", "    not check to see whether those are in the word\n", "    in a vowel context.\n", "    \"\"\"\n", "    vowel_list = ['a', 'e', 'i', 'o', 'u']\n", "    \n", "    for vowel in vowel_list:\n", "        if vowel in word:\n", "            return True\n", "    # If there is a vowel in the word, the function returns, preventing anything after this loop from running\n", "    return False"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["How surprising, an english word contains a vowel.\n"]}], "source": ["my_word = 'catnapping'\n", "if has_a_vowel(my_word):\n", "    print('How surprising, an english word contains a vowel.')\n", "else:\n", "    print('This is actually surprising.')"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [], "source": ["def point_maker(x, y):\n", "    \"\"\" Groups x and y values into a point, technically a tuple \"\"\"\n", "    return x, y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This above function returns an ordered pair of the input parameters, stored as a tuple."]}, {"cell_type": "code", "execution_count": 107, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The slope between a and b is -1.4\n"]}], "source": ["a = point_maker(0, 10)\n", "b = point_maker(5, 3)\n", "def calculate_slope(point_a, point_b):\n", "    \"\"\" Calculates the linear slope between two points \"\"\"\n", "    return (point_b[1] - point_a[1])/(point_b[0] - point_a[0])\n", "print(\"The slope between a and b is {0}\".format(calculate_slope(a, b)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And that one calculates the slope between two points!"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The slope-intercept form of the line between a and b, using point a, is: y - 10 = -1.4(x - 0)\n"]}], "source": ["print(\"The slope-intercept form of the line between a and b, using point a, is: y - {0} = {2}(x - {1})\".format(a[1], a[0], calculate_slope(a, b)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With the proper syntax, you can define functions to do whatever calculations you want. This makes them an indispensible part of programming in any language."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "This was a lot of material and there is still even more to cover! Make sure you play around with the cells in each notebook to accustom yourself to the syntax featured here and to figure out any limitations. If you want to delve even deeper into the material, the [documentation for Python](https://docs.python.org/3/) is all available online."]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "**Next Lecture**: [Introduction to Num<PERSON><PERSON>](Lecture03-Introduction-to-NumPy.ipynb) \n", "\n", "[Back to Introduction](Introduction.ipynb) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "*This presentation is for informational purposes only and does not constitute an offer to sell, a solicitation to buy, or a recommendation for any security; nor does it constitute an offer to provide investment advisory or other services by QuantRocket LLC (\"QuantRocket\"). Nothing contained herein constitutes investment advice or offers any opinion with respect to the suitability of any security, and any views expressed herein should not be taken as advice to buy, sell, or hold any security or as an endorsement of any security or company.  In preparing the information contained herein, the authors have not taken into account the investment needs, objectives, and financial circumstances of any particular investor. Any views expressed and data illustrated herein were prepared based upon information believed to be reliable at the time of publication. QuantRocket makes no guarantees as to their accuracy or completeness. All information is subject to change and may quickly become unreliable for various reasons, including changes in market conditions or economic circumstances.*"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}