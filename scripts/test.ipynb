{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9379f5e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-01-26</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.299999</td>\n", "      <td>23.320000</td>\n", "      <td>22.299999</td>\n", "      <td>22.370001</td>\n", "      <td>112672055.0</td>\n", "      <td>2.558576e+09</td>\n", "      <td>22.309999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-27</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.309999</td>\n", "      <td>23.469999</td>\n", "      <td>22.309999</td>\n", "      <td>23.080000</td>\n", "      <td>129415272.0</td>\n", "      <td>2.976801e+09</td>\n", "      <td>22.780001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-28</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.780001</td>\n", "      <td>23.180000</td>\n", "      <td>22.450001</td>\n", "      <td>22.809999</td>\n", "      <td>85747696.0</td>\n", "      <td>1.948881e+09</td>\n", "      <td>22.809999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-29</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.809999</td>\n", "      <td>23.540001</td>\n", "      <td>22.709999</td>\n", "      <td>23.090000</td>\n", "      <td>124025841.0</td>\n", "      <td>2.864101e+09</td>\n", "      <td>23.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-02-01</th>\n", "      <th>000001.XSHE</th>\n", "      <td>23.000000</td>\n", "      <td>24.990000</td>\n", "      <td>22.700001</td>\n", "      <td>24.549999</td>\n", "      <td>147523930.0</td>\n", "      <td>3.529557e+09</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             open       high        low      close  \\\n", "date       asset                                                     \n", "2021-01-26 000001.XSHE  22.299999  23.320000  22.299999  22.370001   \n", "2021-01-27 000001.XSHE  22.309999  23.469999  22.309999  23.080000   \n", "2021-01-28 000001.XSHE  22.780001  23.180000  22.450001  22.809999   \n", "2021-01-29 000001.XSHE  22.809999  23.540001  22.709999  23.090000   \n", "2021-02-01 000001.XSHE  23.000000  24.990000  22.700001  24.549999   \n", "\n", "                             volume        amount      price  \n", "date       asset                                              \n", "2021-01-26 000001.XSHE  112672055.0  2.558576e+09  22.309999  \n", "2021-01-27 000001.XSHE  129415272.0  2.976801e+09  22.780001  \n", "2021-01-28 000001.XSHE   85747696.0  1.948881e+09  22.809999  \n", "2021-01-29 000001.XSHE  124025841.0  2.864101e+09  23.000000  \n", "2021-02-01 000001.XSHE  147523930.0  3.529557e+09        NaN  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.insert(0, \"scripts\")\n", "\n", "start = datetime.date(2021, 1, 1)\n", "end = datetime.date(2021, 2,1)\n", "lb = load_bars(start, end, 1)\n", "lb.tail()"]}, {"cell_type": "code", "execution_count": 2, "id": "3d4d8e5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.126</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.30</td>\n", "      <td>23.32</td>\n", "      <td>22.30</td>\n", "      <td>22.37</td>\n", "      <td>1126720.55</td>\n", "      <td>2558575.511</td>\n", "      <td>22.31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.127</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.31</td>\n", "      <td>23.47</td>\n", "      <td>22.31</td>\n", "      <td>23.08</td>\n", "      <td>1294152.72</td>\n", "      <td>2976800.955</td>\n", "      <td>22.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.128</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.78</td>\n", "      <td>23.18</td>\n", "      <td>22.45</td>\n", "      <td>22.81</td>\n", "      <td>857476.96</td>\n", "      <td>1948881.146</td>\n", "      <td>22.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.129</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.81</td>\n", "      <td>23.54</td>\n", "      <td>22.71</td>\n", "      <td>23.09</td>\n", "      <td>1240258.41</td>\n", "      <td>2864101.419</td>\n", "      <td>23.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.201</th>\n", "      <th>000001.XSHE</th>\n", "      <td>23.00</td>\n", "      <td>24.99</td>\n", "      <td>22.70</td>\n", "      <td>24.55</td>\n", "      <td>1475239.30</td>\n", "      <td>3529556.986</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      open   high    low  close      volume  \\\n", "date                    asset                                                 \n", "1970-01-01 05:36:50.126 000001.XSHE  22.30  23.32  22.30  22.37  1126720.55   \n", "1970-01-01 05:36:50.127 000001.XSHE  22.31  23.47  22.31  23.08  1294152.72   \n", "1970-01-01 05:36:50.128 000001.XSHE  22.78  23.18  22.45  22.81   857476.96   \n", "1970-01-01 05:36:50.129 000001.XSHE  22.81  23.54  22.71  23.09  1240258.41   \n", "1970-01-01 05:36:50.201 000001.XSHE  23.00  24.99  22.70  24.55  1475239.30   \n", "\n", "                                          amount  price  \n", "date                    asset                            \n", "1970-01-01 05:36:50.126 000001.XSHE  2558575.511  22.31  \n", "1970-01-01 05:36:50.127 000001.XSHE  2976800.955  22.78  \n", "1970-01-01 05:36:50.128 000001.XSHE  1948881.146  22.81  \n", "1970-01-01 05:36:50.129 000001.XSHE  2864101.419  23.00  \n", "1970-01-01 05:36:50.201 000001.XSHE  3529556.986    NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["lbt = load_bars_tushare(start, end, [\"000001.XSHE\"])\n", "lbt.tail()"]}, {"cell_type": "code", "execution_count": 5, "id": "bdc814e4", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Can only compare identically-labeled (both index and columns) DataFrame objects", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[32m/var/folders/b5/73vzvtdn4pn_8wt6rpd2tb_w0000gn/T/ipykernel_80538/565329465.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m lbt.compare(lb)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, align_axis, keep_shape, keep_equal, result_names)\u001b[39m\n\u001b[32m   8596\u001b[39m         keep_shape: bool = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m   8597\u001b[39m         keep_equal: bool = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m   8598\u001b[39m         result_names: Suffixes = (\u001b[33m\"self\"\u001b[39m, \u001b[33m\"other\"\u001b[39m),\n\u001b[32m   8599\u001b[39m     ) -> DataFrame:\n\u001b[32m-> \u001b[39m\u001b[32m8600\u001b[39m         return super().compare(\n\u001b[32m   8601\u001b[39m             other=other,\n\u001b[32m   8602\u001b[39m             align_axis=align_axis,\n\u001b[32m   8603\u001b[39m             keep_shape=keep_shape,\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/generic.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, align_axis, keep_shape, keep_equal, result_names)\u001b[39m\n\u001b[32m  10136\u001b[39m             raise TypeError(\n\u001b[32m  10137\u001b[39m                 f\"can only compare '{cls_self}' (not '{cls_other}') with '{cls_self}'\"\n\u001b[32m  10138\u001b[39m             )\n\u001b[32m  10139\u001b[39m \n\u001b[32m> \u001b[39m\u001b[32m10140\u001b[39m         mask = ~((self == other) | (self.isna() & other.isna()))\n\u001b[32m  10141\u001b[39m         mask.fillna(\u001b[38;5;28;01mTrue\u001b[39;00m, inplace=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m  10142\u001b[39m \n\u001b[32m  10143\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28;01mnot\u001b[39;00m keep_equal:\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/ops/common.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m     72\u001b[39m                     \u001b[38;5;28;01mreturn\u001b[39;00m NotImplemented\n\u001b[32m     73\u001b[39m \n\u001b[32m     74\u001b[39m         other = item_from_zerodim(other)\n\u001b[32m     75\u001b[39m \n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m method(self, other)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/arraylike.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m     38\u001b[39m     @unpack_zerodim_and_defer(\u001b[33m\"__eq__\"\u001b[39m)\n\u001b[32m     39\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m __eq__(self, other):\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m self._cmp_method(other, operator.eq)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, op)\u001b[39m\n\u001b[32m   7901\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m _cmp_method(self, other, op):\n\u001b[32m   7902\u001b[39m         axis: Literal[\u001b[32m1\u001b[39m] = \u001b[32m1\u001b[39m  \u001b[38;5;66;03m# only relevant for Series other case\u001b[39;00m\n\u001b[32m   7903\u001b[39m \n\u001b[32m-> \u001b[39m\u001b[32m7904\u001b[39m         self, other = self._align_for_op(other, axis, flex=\u001b[38;5;28;01mFalse\u001b[39;00m, level=\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m   7905\u001b[39m \n\u001b[32m   7906\u001b[39m         \u001b[38;5;66;03m# See GH#4537 for discussion of scalar op behavior\u001b[39;00m\n\u001b[32m   7907\u001b[39m         new_data = self._dispatch_frame_op(other, op, axis=axis)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, axis, flex, level)\u001b[39m\n\u001b[32m   8199\u001b[39m                     left, right = left.align(\n\u001b[32m   8200\u001b[39m                         right, join=\u001b[33m\"outer\"\u001b[39m, level=level, copy=\u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m   8201\u001b[39m                     )\n\u001b[32m   8202\u001b[39m                 \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m8203\u001b[39m                     raise ValueError(\n\u001b[32m   8204\u001b[39m                         \u001b[33m\"Can only compare identically-labeled (both index and columns) \"\u001b[39m\n\u001b[32m   8205\u001b[39m                         \u001b[33m\"DataFrame objects\"\u001b[39m\n\u001b[32m   8206\u001b[39m                     )\n", "\u001b[31mValueError\u001b[39m: Can only compare identically-labeled (both index and columns) DataFrame objects"]}], "source": ["lbt.compare(lb)"]}, {"cell_type": "code", "execution_count": null, "id": "737171d3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "zillionare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}