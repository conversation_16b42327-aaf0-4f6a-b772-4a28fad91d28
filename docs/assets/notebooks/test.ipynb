{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import duckdb as db\n", "\n", "store = db.connect(\"/tmp/provision.db\")\n", "\n", "for sql in [\n", "    \"CREATE SEQUENCE if not exists id_provision START 1;\",\n", "    \"create table if not exists provision (id INT default nextval('id_provision'), created date default current_date(), name varchar, course varchar, contact varchar, expired date, memo varchar)\"\n", "]:\n", "    store.execute(sql)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store.execute(\"insert into provision (name, password, course, contact, expired, memo) values (?, ?, ?, ?, ?, ?)\", [\"aaron\", \"24lecture\", \"wx\", None, None])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store.execute(\"from provision\").fetch_df()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 2}